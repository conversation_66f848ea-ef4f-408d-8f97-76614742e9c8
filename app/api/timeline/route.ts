import { NextRequest, NextResponse } from 'next/server'
import { createServerSupabaseClient } from '@/lib/supabase/server'

export async function GET(request: NextRequest) {
  try {
    const supabase = await createServerSupabaseClient()

    // Get current user
    const { data: { user } } = await supabase.auth.getUser()

    // Fetch diary entries
    const { data: diaryEntries } = await supabase
      .from('diary_entries')
      .select(`
        id,
        title,
        body_md,
        created_at,
        is_free,
        love_count,
        view_count,
        user_id,
        user:users!user_id (
          id,
          name,
          avatar,
          profile_picture_url,
          has_day1_badge,
          signup_number,
          badge_tier
        ),
        photos (
          id,
          url,
          alt_text
        )
      `)
      .eq('is_hidden', false)
      .order('created_at', { ascending: false })
      .limit(20)

    if (diaryError) {
      console.error('Error fetching diary entries:', diaryError)
    }

    // Fetch audio posts
    const { data: audioPosts, error: audioError } = await supabase
      .from('audio_posts')
      .select(`
        id,
        audio_url,
        description,
        duration_seconds,
        love_count,
        reply_count,
        created_at,
        user:users!user_id (
          id,
          name,
          avatar,
          profile_picture_url,
          has_day1_badge,
          signup_number,
          badge_tier
        )
      `)
      .order('created_at', { ascending: false })
      .limit(30)

    if (audioError) {
      console.error('Error fetching audio posts:', audioError)
    }

    // Fetch book releases (completed books only)
    const { data: bookReleases, error: bookError } = await supabase
      .from('projects')
      .select(`
        id,
        title,
        description,
        cover_image_url,
        genre,
        price_amount,
        average_rating,
        review_count,
        sales_count,
        created_at,
        author_name,
        is_ebook,
        slug,
        user_id,
        user:users!user_id (
          id,
          name,
          avatar,
          profile_picture_url,
          has_day1_badge,
          signup_number,
          badge_tier
        )
      `)
      .eq('is_ebook', true)
      .eq('is_complete', true)
      .eq('is_private', false)
      .order('created_at', { ascending: false })
      .limit(limit)

    if (bookError) {
      console.error('Error fetching book releases:', bookError)
    }

    // Get follow and subscription status if user is logged in
    let followingIds: Set<string> = new Set()
    let subscribedIds: Set<string> = new Set()

    if (user) {
      // Get follows
      const { data: follows } = await supabase
        .from('follows')
        .select('writer_id')
        .eq('follower_id', user.id)

      followingIds = new Set(follows?.map(f => f.writer_id) || [])

      // Get subscriptions
      const { data: subscriptions } = await supabase
        .from('subscriptions')
        .select('writer_id')
        .eq('reader_id', user.id)
        .eq('status', 'active')

      subscribedIds = new Set(subscriptions?.map(s => s.writer_id) || [])
    }

    // Show content from followed/subscribed creators, plus some general content if no follows
    let filteredBookReleases = bookReleases || []
    let filteredDiaryEntries = diaryEntries || []
    let filteredAudioPosts = audioPosts || []

    if (user && (followingIds.size > 0 || subscribedIds.size > 0)) {
      // User has follows/subscriptions - show content from followed creators plus some general content
      filteredBookReleases = (bookReleases || []).filter(book =>
        subscribedIds.has(book.user_id) || followingIds.has(book.user_id)
      )
      // Also include some recent general content (limit to avoid overwhelming)
      const generalBooks = (bookReleases || []).filter(book =>
        !subscribedIds.has(book.user_id) && !followingIds.has(book.user_id)
      ).slice(0, 3)
      filteredBookReleases = [...filteredBookReleases, ...generalBooks]
    } else if (user) {
      // User has no follows - show recent general content
      filteredBookReleases = (bookReleases || []).slice(0, 10)
      filteredDiaryEntries = (diaryEntries || []).slice(0, 15)
      filteredAudioPosts = (audioPosts || []).slice(0, 10)
    } else {
      // Non-authenticated users get limited general content
      filteredBookReleases = (bookReleases || []).slice(0, 5)
      filteredDiaryEntries = (diaryEntries || []).slice(0, 10)
      filteredAudioPosts = (audioPosts || []).slice(0, 5)
    }

    // Combine and format posts
    const combinedPosts = [
      ...filteredDiaryEntries.map(entry => ({
        ...entry,
        type: 'diary' as const,
        isFollowing: user ? followingIds.has(entry.user.id) : false,
        isSubscribed: user ? subscribedIds.has(entry.user.id) : false
      })),
      ...filteredAudioPosts.map(post => ({
        ...post,
        type: 'audio' as const,
        isFollowing: user ? followingIds.has(post.user.id) : false,
        isSubscribed: user ? subscribedIds.has(post.user.id) : false
      })),
      ...filteredBookReleases.map(book => ({
        ...book,
        type: 'book' as const,
        isFollowing: user ? followingIds.has(book.user.id) : false,
        isSubscribed: user ? subscribedIds.has(book.user.id) : false
      }))
    ]

    // Sort by created_at (most recent first)
    combinedPosts.sort((a, b) => 
      new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
    )

    // Apply offset and limit to combined results
    const paginatedPosts = combinedPosts.slice(offset, offset + limit)

    return NextResponse.json({ 
      posts: paginatedPosts,
      hasMore: combinedPosts.length > offset + limit
    })
  } catch (error) {
    console.error('Timeline API error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
